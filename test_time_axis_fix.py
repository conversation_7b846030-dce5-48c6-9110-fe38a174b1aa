#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试时间轴修复效果
"""

import sys
import os
sys.path.append('已处理-工况')

from visual import generate_rigsta_only_plot
import pandas as pd

def test_time_axis_fix():
    """测试时间轴修复效果，特别是单一工况数据"""
    
    # 选择测试文件
    test_files = [
        "已处理-工况/充浅1实时数据.csv",
        "已处理-工况/宜203H1-2实时数据.csv",
        "已处理-工况/资216实时数据.csv"
    ]
    
    # 创建测试输出目录
    test_output = "test_time_axis_fix"
    os.makedirs(test_output, exist_ok=True)
    
    print("=== 测试时间轴修复效果 ===\n")
    
    for test_file in test_files:
        if not os.path.exists(test_file):
            print(f"⚠️  文件不存在: {test_file}")
            continue
            
        try:
            # 读取数据分析时间跨度和工况分布
            df = pd.read_csv(test_file, encoding='utf-8-sig')
            df['date'] = pd.to_datetime(df['date'])
            
            file_name = os.path.basename(test_file).replace('.csv', '')
            print(f"📊 分析文件: {file_name}")
            print(f"   数据行数: {len(df):,}")
            
            # 计算时间跨度
            time_span = df['date'].max() - df['date'].min()
            total_hours = time_span.total_seconds() / 3600
            print(f"   时间跨度: {time_span} ({total_hours:.1f} 小时)")
            
            # 分析工况分布
            if 'RIGSTA' in df.columns:
                rigsta_counts = df['RIGSTA'].value_counts()
                print(f"   工况类型: {len(rigsta_counts)} 种")
                
                # 显示主要工况
                for rigsta, count in rigsta_counts.head(3).items():
                    percentage = (count / len(df)) * 100
                    print(f"     - {rigsta}: {count:,} 次 ({percentage:.1f}%)")
                
                # 检查是否为单一工况
                is_single_rigsta = len(rigsta_counts) == 1
                if is_single_rigsta:
                    print(f"   ⚠️  单一工况数据: {rigsta_counts.index[0]}")
            else:
                print("   ❌ 没有RIGSTA列")
                continue
            
            # 测试条带图模式（推荐用于解决时间轴问题）
            print(f"   🔄 生成优化的工况图...")
            
            result = generate_rigsta_only_plot(
                csv_path=test_file,
                output_folder=test_output,
                figsize=(24, 8),  # 增大宽度以提供更多时间轴空间
                dpi=200,
                visualization_mode="strip"
            )
            
            if result:
                # 重命名文件以标识修复版本
                original_name = result
                new_name = original_name.replace('.png', '_time_axis_fixed.png')
                os.rename(original_name, new_name)
                print(f"   ✅ 时间轴修复版本生成: {os.path.basename(new_name)}")
            else:
                print(f"   ❌ 生成失败")
                
        except Exception as e:
            print(f"   ❌ 处理失败: {str(e)}")
            import traceback
            traceback.print_exc()
        
        print()  # 空行分隔
    
    print(f"📂 测试图表保存在: {os.path.abspath(test_output)}")
    print("\n=== 时间轴修复说明 ===")
    print("✅ 智能时间间隔：根据数据时间跨度自动调整刻度间隔")
    print("✅ 动态标签格式：短时间用时:分，长时间用日期")
    print("✅ 防重叠机制：自动调整标签旋转角度和字体大小")
    print("✅ 单一工况优化：特殊处理单一工况数据的显示")
    print("✅ 图表宽度适配：根据图表宽度计算合理的标签密度")

if __name__ == "__main__":
    test_time_axis_fix()
