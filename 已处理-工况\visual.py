import pandas as pd
import matplotlib.pyplot as plt
from pathlib import Path
from matplotlib.ticker import ScalarFormatter
from matplotlib import dates as mdates
import numpy as np
# ========== 中文字体配置 ==========
plt.rcParams['font.sans-serif'] = ['SimHei']  # Windows系统字体
plt.rcParams['axes.unicode_minus'] = False    # 解决负号显示异常

# ========== 工况数值映射 ==========
RIGSTA_MAPPING = {
    '下钻': 1,
    '停钻': 2,
    '划眼': 3,
    '坐卡': 4,
    '循环': 5,
    '接单根': 6,
    '接立柱': 7,
    '接触井底': 8,
    '接触井底/循环': 9,
    '提离井底': 10,
    '提离井底/循环': 11,
    '未定义': 12,
    '未知': 13,
    '等待': 14,
    '起下钻': 15,
    '起钻': 16,
    '钻进': 17,
}

# 工况颜色映射（使用不同颜色区分不同工况）
RIGSTA_COLORS = {
    1: '#FF6B6B',   # 下钻 - 红色
    2: '#4ECDC4',   # 停钻 - 青色
    3: '#45B7D1',   # 划眼 - 蓝色
    4: '#96CEB4',   # 坐卡 - 绿色
    5: '#FFEAA7',   # 循环 - 黄色
    6: '#DDA0DD',   # 接单根 - 紫色
    7: '#98D8C8',   # 接立柱 - 薄荷绿
    8: '#F7DC6F',   # 接触井底 - 金黄色
    9: '#BB8FCE',   # 接触井底/循环 - 淡紫色
    10: '#85C1E9',  # 提离井底 - 天蓝色
    11: '#F8C471',  # 提离井底/循环 - 橙色
    12: '#D5DBDB',  # 未定义 - 灰色
    13: '#BDC3C7',  # 未知 - 浅灰色
    14: '#F1948A',  # 等待 - 粉红色
    15: '#82E0AA',  # 起下钻 - 浅绿色
    16: '#AED6F1',  # 起钻 - 浅蓝色
    17: '#F9E79F',  # 钻进 - 浅黄色
}

def convert_rigsta_to_numeric(rigsta_series):
    """
    将工况文本转换为数值

    :param rigsta_series: pandas Series，包含工况文本数据
    :return: pandas Series，包含对应的数值
    """
    return rigsta_series.map(RIGSTA_MAPPING).fillna(0)  # 未知工况映射为0

def setup_time_axis(ax, df, figure_width=22):
    """
    智能设置时间轴格式，根据数据时间跨度自动调整

    :param ax: matplotlib轴对象
    :param df: 包含date列的DataFrame
    :param figure_width: 图表宽度（英寸），用于计算标签密度
    """
    if 'date' not in df.columns or df['date'].isna().all():
        return

    # 计算时间跨度
    time_span = pd.to_datetime(df['date'].max()) - pd.to_datetime(df['date'].min())
    total_hours = time_span.total_seconds() / 3600
    total_days = time_span.days

    # 根据图表宽度估算合理的标签数量（避免重叠）
    max_labels = max(6, int(figure_width * 0.8))  # 每英寸约0.8个标签

    # 根据时间跨度智能选择刻度间隔和格式
    if total_hours <= 6:  # 6小时以内
        major_interval = max(1, int(total_hours / max_labels))
        ax.xaxis.set_major_locator(mdates.HourLocator(interval=major_interval))
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%H:%M'))
        ax.xaxis.set_minor_locator(mdates.MinuteLocator(interval=30))
        rotation = 0
    elif total_hours <= 24:  # 1天以内
        major_interval = max(1, int(total_hours / max_labels))
        ax.xaxis.set_major_locator(mdates.HourLocator(interval=major_interval))
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%m-%d %H:%M'))
        ax.xaxis.set_minor_locator(mdates.HourLocator(interval=1))
        rotation = 30
    elif total_days <= 7:  # 1周以内
        major_interval = max(3, int(total_hours / max_labels))
        ax.xaxis.set_major_locator(mdates.HourLocator(interval=major_interval))
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%m-%d %H:%M'))
        ax.xaxis.set_minor_locator(mdates.HourLocator(interval=1))
        rotation = 45
    elif total_days <= 30:  # 1月以内
        major_interval = max(1, int(total_days / max_labels))
        ax.xaxis.set_major_locator(mdates.DayLocator(interval=major_interval))
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%m-%d'))
        ax.xaxis.set_minor_locator(mdates.HourLocator(interval=6))
        rotation = 45
    else:  # 超过1月
        major_interval = max(1, int(total_days / max_labels))
        ax.xaxis.set_major_locator(mdates.DayLocator(interval=major_interval))
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
        ax.xaxis.set_minor_locator(mdates.DayLocator(interval=1))
        rotation = 45

    # 设置标签旋转和字体
    ax.tick_params(axis='x', rotation=rotation, labelsize=10)

    # 优化标签显示
    ax.xaxis.set_tick_params(which='major', pad=8)

    # 如果标签仍然可能重叠，进一步减少标签数量
    if total_hours > 48:  # 超过2天的数据，使用更稀疏的标签
        ax.xaxis.set_major_locator(mdates.AutoDateLocator(maxticks=max_labels))
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%m-%d %H:%M'))
        ax.tick_params(axis='x', rotation=45, labelsize=9)

def _plot_rigsta_strip(ax, df_plot, unique_rigsta):
    """
    条带图模式：将工况显示为水平条带
    """
    # 识别工况变化点
    df_plot = df_plot[df_plot['RIGSTA_NUM'] > 0].copy()
    if len(df_plot) == 0:
        return

    # 找到工况变化的时间点
    df_plot = df_plot.sort_values('date')
    df_plot['rigsta_change'] = df_plot['RIGSTA_NUM'] != df_plot['RIGSTA_NUM'].shift(1)

    # 为每个工况段创建条带
    current_rigsta = None
    start_time = None

    for _, row in df_plot.iterrows():
        if row['rigsta_change'] or current_rigsta is None:
            # 如果不是第一个段，绘制前一个段
            if current_rigsta is not None and start_time is not None:
                color = RIGSTA_COLORS.get(current_rigsta, '#BDC3C7')
                duration = (row['date'] - start_time).total_seconds() / 3600
                ax.barh(current_rigsta, duration, left=start_time, height=0.8,
                       color=color, alpha=0.8, edgecolor='white', linewidth=0.5)

            # 开始新的段
            current_rigsta = row['RIGSTA_NUM']
            start_time = row['date']

    # 绘制最后一个段
    if current_rigsta is not None and start_time is not None:
        end_time = df_plot['date'].iloc[-1]
        color = RIGSTA_COLORS.get(current_rigsta, '#BDC3C7')
        ax.barh(current_rigsta, (end_time - start_time).total_seconds() / 3600,
               left=start_time, height=0.8, color=color, alpha=0.8,
               edgecolor='white', linewidth=0.5)

    # 添加图例
    legend_elements = []
    for rigsta_text in unique_rigsta:
        rigsta_num = RIGSTA_MAPPING.get(rigsta_text, 0)
        if rigsta_num > 0:
            color = RIGSTA_COLORS.get(rigsta_num, '#BDC3C7')
            legend_elements.append(plt.Rectangle((0,0),1,1, facecolor=color,
                                               alpha=0.8, label=rigsta_text))

    if legend_elements:
        ax.legend(handles=legend_elements, bbox_to_anchor=(1.02, 1),
                 loc='upper left', fontsize=10)

def _plot_rigsta_scatter_optimized(ax, df_plot, unique_rigsta):
    """
    优化的散点图模式：大点、高对比度、智能采样
    """
    # 数据采样：如果数据点过多，进行采样以减少重叠
    valid_data = df_plot[df_plot['RIGSTA_NUM'] > 0].copy()
    if len(valid_data) > 5000:  # 如果数据点超过5000个，进行采样
        sample_rate = max(1, len(valid_data) // 3000)
        valid_data = valid_data.iloc[::sample_rate]

    # 绘制连线显示工况变化趋势
    if len(valid_data) > 1:
        ax.plot(valid_data['date'], valid_data['RIGSTA_NUM'],
                color='#666666', alpha=0.4, linewidth=1, zorder=1)

    # 绘制散点图，使用更大的点和更强的对比度
    for rigsta_text in unique_rigsta:
        rigsta_num = RIGSTA_MAPPING.get(rigsta_text, 0)
        if rigsta_num > 0:
            mask = valid_data['RIGSTA'] == rigsta_text
            if mask.any():
                color = RIGSTA_COLORS.get(rigsta_num, '#BDC3C7')
                ax.scatter(valid_data.loc[mask, 'date'],
                          valid_data.loc[mask, 'RIGSTA_NUM'],
                          c=color, label=rigsta_text, alpha=0.9, s=25,
                          edgecolors='white', linewidth=0.8, zorder=2)

def _plot_rigsta_hybrid(ax, df_plot, unique_rigsta):
    """
    混合模式：条带图 + 工况变化点标记
    """
    # 先绘制条带图
    _plot_rigsta_strip(ax, df_plot, unique_rigsta)

    # 添加工况变化点的垂直线标记
    df_plot = df_plot[df_plot['RIGSTA_NUM'] > 0].copy()
    if len(df_plot) == 0:
        return

    df_plot = df_plot.sort_values('date')
    df_plot['rigsta_change'] = df_plot['RIGSTA_NUM'] != df_plot['RIGSTA_NUM'].shift(1)

    # 在工况变化点添加垂直线
    change_points = df_plot[df_plot['rigsta_change']]['date']
    for change_time in change_points:
        ax.axvline(x=change_time, color='red', alpha=0.6, linewidth=1.5,
                  linestyle='--', zorder=3)

def _plot_single_rigsta_optimized(ax, df_plot, rigsta_text, visualization_mode):
    """
    单一工况的优化显示：专门处理只有一种工况的情况
    """
    rigsta_num = RIGSTA_MAPPING.get(rigsta_text, 0)
    if rigsta_num <= 0:
        return

    valid_data = df_plot[df_plot['RIGSTA_NUM'] == rigsta_num].copy()
    if len(valid_data) == 0:
        return

    color = RIGSTA_COLORS.get(rigsta_num, '#BDC3C7')

    if visualization_mode == "strip":
        # 单一工况条带图：显示为一个连续的条带
        start_time = valid_data['date'].min()
        end_time = valid_data['date'].max()
        duration = (end_time - start_time).total_seconds() / 3600

        ax.barh(rigsta_num, duration, left=start_time, height=0.6,
               color=color, alpha=0.8, edgecolor='white', linewidth=1,
               label=rigsta_text)

        # 添加一些视觉细节
        ax.text(start_time + pd.Timedelta(hours=duration/2), rigsta_num,
               f'{rigsta_text}\n({len(valid_data):,} 数据点)',
               ha='center', va='center', fontsize=11, fontweight='bold',
               bbox=dict(boxstyle='round,pad=0.3', facecolor='white', alpha=0.8))

    else:
        # 散点图模式：使用采样避免过度密集
        if len(valid_data) > 1000:
            sample_rate = max(1, len(valid_data) // 500)
            valid_data = valid_data.iloc[::sample_rate]

        ax.scatter(valid_data['date'], valid_data['RIGSTA_NUM'],
                  c=color, alpha=0.7, s=15, edgecolors='white', linewidth=0.5,
                  label=f'{rigsta_text} ({len(df_plot[df_plot["RIGSTA_NUM"] == rigsta_num]):,} 点)')

        # 添加连线显示连续性
        ax.plot(valid_data['date'], valid_data['RIGSTA_NUM'],
               color=color, alpha=0.3, linewidth=2)

def plot_rigsta_timeline(ax, df, title_suffix="", visualization_mode="strip"):
    """
    绘制工况时间轴图

    :param ax: matplotlib轴对象
    :param df: 包含date和RIGSTA列的DataFrame
    :param title_suffix: 标题后缀
    :param visualization_mode: 可视化模式 ("strip", "scatter", "hybrid")
    """
    # 转换工况为数值
    df_plot = df.copy()
    df_plot['RIGSTA_NUM'] = convert_rigsta_to_numeric(df_plot['RIGSTA'])

    # 获取唯一的工况类型
    unique_rigsta = df_plot['RIGSTA'].dropna().unique()
    unique_nums = [RIGSTA_MAPPING.get(r, 0) for r in unique_rigsta]

    # 特殊处理：如果只有单一工况，优化显示
    if len(unique_rigsta) == 1:
        _plot_single_rigsta_optimized(ax, df_plot, unique_rigsta[0], visualization_mode)
    else:
        if visualization_mode == "strip":
            # 条带图模式：显示工况持续时间
            _plot_rigsta_strip(ax, df_plot, unique_rigsta)
        elif visualization_mode == "hybrid":
            # 混合模式：条带 + 变化点标记
            _plot_rigsta_hybrid(ax, df_plot, unique_rigsta)
        else:
            # 优化的散点图模式
            _plot_rigsta_scatter_optimized(ax, df_plot, unique_rigsta)

    # 设置Y轴标签，增大字体和间距
    y_ticks = sorted(unique_nums)
    y_labels = [list(RIGSTA_MAPPING.keys())[list(RIGSTA_MAPPING.values()).index(num)]
                for num in y_ticks if num > 0]

    ax.set_yticks(y_ticks)
    ax.set_yticklabels(y_labels, fontsize=12)
    ax.set_ylabel('钻井工况', rotation=0, labelpad=60, ha='right', fontsize=13)

    # 只在有标题后缀时才设置子图标题
    if title_suffix:
        ax.set_title(f'钻井工况变化{title_suffix}', fontsize=12)

    # 设置图例，优化显示（单一工况时不显示图例）
    if len(unique_rigsta) > 1 and len(unique_rigsta) <= 12:
        ax.legend(bbox_to_anchor=(1.02, 1), loc='upper left', fontsize=10,
                 frameon=True, fancybox=True, shadow=True)

    # 设置网格和样式
    ax.grid(True, alpha=0.2, linestyle='--', linewidth=0.5)

    # 动态调整Y轴范围
    if len(unique_nums) == 1:
        # 单一工况时，给更多的垂直空间
        ax.set_ylim(unique_nums[0] - 0.5, unique_nums[0] + 0.5)
    else:
        ax.set_ylim(0.5, max(RIGSTA_MAPPING.values()) + 0.5)

    # 优化背景色，使工况变化更突出
    ax.set_facecolor('#f8f9fa')

def generate_trend_plot(
    csv_path: str,
    output_folder: str,
    time_range: str = None,
    figsize: tuple = (22, 24),  # 减少高度，因为不包含工况子图
    dpi: int = 300
):
    """
    :param csv_path: CSV文件路径
    :param output_folder: 图片输出目录
    :param time_range: 时间范围字符串（用于标题）
    :param figsize: 图表尺寸
    :param dpi: 图片分辨率
    """
    # ========== 参数校验 ==========
    csv_file = Path(csv_path)
    if not csv_file.exists():
        raise FileNotFoundError(f"CSV文件不存在: {csv_path}")
    
    name_map = {
    # 原有特征
    'DEP': '井深',
    'BITDEP': '钻头位置',
    'HOKHEI': '大钩负荷',
    'DRITIME': '钻时',
    'WOB': '钻压',
    'HKLD': '大钩高度',
    'RPM': '转速',
    'TOR': '扭矩',

    # 新增特征
    'FLOWIN': '入口流量',
    'FLOWOUT': '出口流量',
    'SPP': '泵压',
    'CSIP': '套压',
    'RIGSTA': '钻井工况'
}

    # ========== 配置输出路径 ==========
    output_dir = Path(output_folder)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # 生成图片文件名（与CSV文件名保持一致）
    img_name = f"{csv_file.stem}.png"
    img_path = output_dir / img_name

    # ========== 数据准备 ==========
    try:
        df = pd.read_csv(csv_file)
        df['date'] = pd.to_datetime(df['date'])
    except Exception as e:
        raise ValueError(f"CSV文件读取失败: {str(e)}")

    # ========== 图表配置 ==========
    plt.style.use('seaborn-v0_8-colorblind')
    numeric_features = [
        'DEP', 'BITDEP', 'HOKHEI', 'DRITIME',
        'WOB', 'HKLD', 'RPM', 'TOR',
        'FLOWIN', 'FLOWOUT', 'SPP', 'CSIP'
    ]

    # 完整趋势图中不包含工况子图，只显示数值参数
    total_plots = len(numeric_features)

    # ========== 创建图表 ==========
    fig, axes = plt.subplots(total_plots, 1, figsize=(22, 1.2*total_plots), sharex=True)

    # 确保axes是数组
    if total_plots == 1:
        axes = [axes]

    # 绘制数值特征
    colors = plt.get_cmap('tab10')
    for i, col in enumerate(numeric_features):
        if col in df.columns:
            axes[i].plot(df['date'], df[col], color=colors(i%10), label=name_map[col])
            axes[i].set_ylabel(name_map[col], rotation=0, labelpad=20, ha='right')
            axes[i].legend(loc='best', frameon=True)
            axes[i].yaxis.set_major_formatter(ScalarFormatter(useOffset=False))
            axes[i].ticklabel_format(style='plain', axis='y')
        else:
            # 如果列不存在，显示空图
            axes[i].text(0.5, 0.5, f'{name_map[col]} - 数据缺失',
                        transform=axes[i].transAxes, ha='center', va='center')
            axes[i].set_ylabel(name_map[col], rotation=0, labelpad=20, ha='right')

    # ========== 时间轴格式设置 ==========
    # 使用智能时间轴设置
    for ax in axes:
        setup_time_axis(ax, df, figsize[0])

    # 确保只有最底部的轴显示X轴标签
    for i, ax in enumerate(axes[:-1]):
        ax.set_xticklabels([])

    # ========== 标题和布局 ==========
    title = f'{csv_file.stem} 参数趋势'
    if time_range:
        title += f' ({time_range})'
    fig.suptitle(title, y=0.98)
    plt.tight_layout(h_pad=1.5)

    # ========== 保存输出 ==========
    plt.savefig(img_path, bbox_inches='tight', dpi=dpi)
    plt.close()
    print(f"✅ 趋势图生成成功: {img_path}")
    return str(img_path)


def generate_rigsta_only_plot(
    csv_path: str,
    output_folder: str,
    time_range: str = None,
    figsize: tuple = (22, 8),
    dpi: int = 300,
    visualization_mode: str = "strip"
):
    """
    专门绘制工况变化图表

    :param csv_path: CSV文件路径
    :param output_folder: 图片输出目录
    :param time_range: 时间范围字符串（用于标题）
    :param figsize: 图表尺寸
    :param dpi: 图片分辨率
    """
    # ========== 参数校验 ==========
    csv_file = Path(csv_path)
    if not csv_file.exists():
        raise FileNotFoundError(f"CSV文件不存在: {csv_path}")

    # ========== 配置输出路径 ==========
    output_dir = Path(output_folder)
    output_dir.mkdir(parents=True, exist_ok=True)

    # 生成图片文件名
    img_name = f"{csv_file.stem}_工况变化.png"
    img_path = output_dir / img_name

    # ========== 数据准备 ==========
    try:
        df = pd.read_csv(csv_file, encoding='utf-8-sig')
        df['date'] = pd.to_datetime(df['date'])
    except Exception as e:
        raise ValueError(f"CSV文件读取失败: {str(e)}")

    # 检查是否有RIGSTA列
    if 'RIGSTA' not in df.columns or df['RIGSTA'].isna().all():
        print(f"警告：文件 {csv_file.name} 中没有有效的RIGSTA数据")
        return None

    # ========== 创建工况图表 ==========
    plt.style.use('seaborn-v0_8-colorblind')
    fig, ax = plt.subplots(1, 1, figsize=figsize)

    # 绘制工况时间轴（不传递标题后缀，避免重复）
    plot_rigsta_timeline(ax, df, visualization_mode=visualization_mode)

    # ========== 时间轴格式设置 ==========
    # 使用智能时间轴设置
    setup_time_axis(ax, df, figsize[0])

    # ========== 标题和布局 ==========
    title = f'{csv_file.stem} 钻井工况变化'
    if time_range:
        title += f' ({time_range})'
    fig.suptitle(title, y=0.95, fontsize=14)
    plt.tight_layout()

    # ========== 保存输出 ==========
    plt.savefig(img_path, bbox_inches='tight', dpi=dpi)
    plt.close()
    print(f"✅ 工况图生成成功: {img_path}")
    return str(img_path)


def batch_process_csv_files(
    input_folder: str = ".",
    output_folder: str = "charts",
    figsize: tuple = (22, 25),
    dpi: int = 200,
    include_rigsta: bool = True,
    rigsta_only: bool = False,
    visualization_mode: str = "strip"
):
    """
    批量处理目录中的所有CSV文件，生成趋势图

    :param input_folder: CSV文件所在目录，默认为当前目录
    :param output_folder: 图片输出目录，默认为 "charts"
    :param figsize: 图表尺寸
    :param dpi: 图片分辨率
    :param include_rigsta: 是否在综合图表中包含工况，默认True
    :param rigsta_only: 是否只生成工况图表，默认False
    """
    # ========== 扫描CSV文件 ==========
    input_path = Path(input_folder)
    csv_files = list(input_path.glob("*.csv"))

    if not csv_files:
        print("❌ 未找到任何CSV文件")
        return

    print(f"📁 发现 {len(csv_files)} 个CSV文件")
    print(f"📊 输出目录: {output_folder}")
    print("-" * 50)

    # ========== 创建输出目录 ==========
    output_path = Path(output_folder)
    output_path.mkdir(parents=True, exist_ok=True)

    # ========== 批量处理 ==========
    success_count = 0
    failed_files = []

    for i, csv_file in enumerate(csv_files, 1):
        try:
            print(f"🔄 正在处理 {i}/{len(csv_files)}: {csv_file.name}")

            if rigsta_only:
                # 只生成工况图表
                generate_rigsta_only_plot(
                    csv_path=str(csv_file),
                    output_folder=output_folder,
                    figsize=(22, 8),
                    dpi=dpi,
                    visualization_mode=visualization_mode
                )
            else:
                # 生成综合趋势图（包含工况）
                generate_trend_plot(
                    csv_path=str(csv_file),
                    output_folder=output_folder,
                    figsize=figsize,
                    dpi=dpi
                )

                # 如果需要，额外生成单独的工况图
                if include_rigsta:
                    try:
                        generate_rigsta_only_plot(
                            csv_path=str(csv_file),
                            output_folder=output_folder,
                            figsize=(22, 8),
                            dpi=dpi,
                            visualization_mode=visualization_mode
                        )
                    except Exception as e:
                        print(f"⚠️  工况图生成失败 {csv_file.name}: {str(e)}")

            success_count += 1

        except Exception as e:
            error_msg = f"❌ 处理失败 {csv_file.name}: {str(e)}"
            print(error_msg)
            failed_files.append((csv_file.name, str(e)))
            continue

    # ========== 处理结果总结 ==========
    print("-" * 50)
    print(f"📈 处理完成！")
    print(f"✅ 成功处理: {success_count} 个文件")
    print(f"❌ 处理失败: {len(failed_files)} 个文件")

    if failed_files:
        print("\n失败文件详情:")
        for filename, error in failed_files:
            print(f"  • {filename}: {error}")

    print(f"\n📂 所有图表已保存到: {output_path.absolute()}")
    return success_count, failed_files

# ===== 使用示例 =====
if __name__ == "__main__":
    print("选择处理模式:")
    print("1. 生成完整趋势图（仅数值参数，不含工况）")
    print("2. 只生成工况变化图")
    print("3. 生成完整趋势图 + 单独工况图（推荐）")

    choice = input("请输入选择 (1/2/3，默认为3): ").strip() or "3"

    # 如果选择包含工况图，询问可视化模式
    visualization_mode = "strip"
    if choice in ["2", "3"]:
        print("\n选择工况可视化模式:")
        print("1. 条带图模式（推荐）- 清晰显示工况持续时间")
        print("2. 散点图模式 - 传统点状显示")
        print("3. 混合模式 - 条带图 + 变化点标记")

        mode_choice = input("请输入选择 (1/2/3，默认为1): ").strip() or "1"
        if mode_choice == "2":
            visualization_mode = "scatter"
        elif mode_choice == "3":
            visualization_mode = "hybrid"
        else:
            visualization_mode = "strip"

    if choice == "1":
        # 生成完整趋势图（不包含工况）
        batch_process_csv_files(
            input_folder=".",
            output_folder="charts",
            figsize=(22, 24),  # 调整高度，因为不包含工况图
            dpi=200,
            include_rigsta=False,  # 不额外生成单独工况图
            rigsta_only=False
        )
    elif choice == "2":
        # 只生成工况变化图
        batch_process_csv_files(
            input_folder=".",
            output_folder="charts",
            figsize=(22, 8),
            dpi=200,
            include_rigsta=False,
            rigsta_only=True,
            visualization_mode=visualization_mode
        )
    else:  # choice == "3" 或其他
        # 生成完整趋势图 + 单独工况图
        batch_process_csv_files(
            input_folder=".",
            output_folder="charts",
            figsize=(22, 24),  # 调整高度，因为完整图不包含工况
            dpi=200,
            include_rigsta=True,  # 额外生成单独工况图
            rigsta_only=False,
            visualization_mode=visualization_mode
        )

    # 单个文件处理示例：
    # generate_trend_plot(
    #     csv_path="your_file.csv",
    #     output_folder="charts",
    #     figsize=(22, 24),  # 调整高度，因为不包含工况图
    #     dpi=200
    # )

    # 单独生成工况图示例：
    # generate_rigsta_only_plot(
    #     csv_path="your_file.csv",
    #     output_folder="charts",
    #     figsize=(22, 8),
    #     dpi=200,
    #     visualization_mode="strip"  # 可选: "strip", "scatter", "hybrid"
    # )