#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试不同可视化模式的效果
"""

import sys
import os
sys.path.append('已处理-工况')

from visual import generate_rigsta_only_plot
import pandas as pd

def test_visualization_modes():
    """测试三种不同的工况可视化模式"""
    
    # 选择测试文件
    test_file = "已处理-工况/充浅1实时数据.csv"
    
    if not os.path.exists(test_file):
        print(f"测试文件不存在: {test_file}")
        return
    
    # 读取数据查看基本信息
    df = pd.read_csv(test_file, encoding='utf-8-sig')
    print(f"=== 测试文件信息 ===")
    print(f"文件: {test_file}")
    print(f"数据行数: {len(df):,}")
    print(f"时间范围: {df['date'].min()} 到 {df['date'].max()}")
    
    if 'RIGSTA' in df.columns:
        rigsta_counts = df['RIGSTA'].value_counts()
        print(f"工况类型分布:")
        for rigsta, count in rigsta_counts.head(10).items():
            print(f"  {rigsta}: {count:,} 次")
        if len(rigsta_counts) > 10:
            print(f"  ... 还有 {len(rigsta_counts) - 10} 种工况类型")
    else:
        print("❌ 没有RIGSTA列，无法测试")
        return
    
    # 创建测试输出目录
    test_output = "test_visualization_modes"
    os.makedirs(test_output, exist_ok=True)
    
    # 测试三种可视化模式
    modes = [
        ("strip", "条带图模式"),
        ("scatter", "散点图模式"), 
        ("hybrid", "混合模式")
    ]
    
    print(f"\n=== 开始测试可视化模式 ===")
    
    for mode, description in modes:
        try:
            print(f"\n🔄 测试 {description} (mode={mode})...")
            
            result = generate_rigsta_only_plot(
                csv_path=test_file,
                output_folder=test_output,
                figsize=(24, 10),  # 稍微增大图表尺寸
                dpi=200,
                visualization_mode=mode
            )
            
            if result:
                # 重命名文件以区分不同模式
                original_name = result
                new_name = original_name.replace('.png', f'_{mode}_mode.png')
                os.rename(original_name, new_name)
                print(f"✅ {description} 生成成功: {new_name}")
            else:
                print(f"❌ {description} 生成失败")
                
        except Exception as e:
            print(f"❌ {description} 测试失败: {str(e)}")
            import traceback
            traceback.print_exc()
    
    print(f"\n📂 测试图表保存在: {os.path.abspath(test_output)}")
    print(f"\n=== 测试完成 ===")
    print("请查看生成的图表，比较不同可视化模式的效果：")
    print("1. strip_mode.png - 条带图模式（推荐用于密集数据）")
    print("2. scatter_mode.png - 散点图模式（传统方式）")
    print("3. hybrid_mode.png - 混合模式（条带+变化点标记）")

if __name__ == "__main__":
    test_visualization_modes()
