import pandas as pd
import matplotlib.pyplot as plt
from pathlib import Path
from matplotlib.ticker import ScalarFormatter
from matplotlib import dates as mdates
import numpy as np
# ========== 中文字体配置 ==========
plt.rcParams['font.sans-serif'] = ['SimHei']  # Windows系统字体
plt.rcParams['axes.unicode_minus'] = False    # 解决负号显示异常

# ========== 工况数值映射 ==========
RIGSTA_MAPPING = {
    '下钻': 1,
    '停钻': 2,
    '划眼': 3,
    '坐卡': 4,
    '循环': 5,
    '接单根': 6,
    '接立柱': 7,
    '接触井底': 8,
    '接触井底/循环': 9,
    '提离井底': 10,
    '提离井底/循环': 11,
    '未定义': 12,
    '未知': 13,
    '等待': 14,
    '起下钻': 15,
    '起钻': 16,
    '钻进': 17,
}

# 工况颜色映射（使用不同颜色区分不同工况）
RIGSTA_COLORS = {
    1: '#FF6B6B',   # 下钻 - 红色
    2: '#4ECDC4',   # 停钻 - 青色
    3: '#45B7D1',   # 划眼 - 蓝色
    4: '#96CEB4',   # 坐卡 - 绿色
    5: '#FFEAA7',   # 循环 - 黄色
    6: '#DDA0DD',   # 接单根 - 紫色
    7: '#98D8C8',   # 接立柱 - 薄荷绿
    8: '#F7DC6F',   # 接触井底 - 金黄色
    9: '#BB8FCE',   # 接触井底/循环 - 淡紫色
    10: '#85C1E9',  # 提离井底 - 天蓝色
    11: '#F8C471',  # 提离井底/循环 - 橙色
    12: '#D5DBDB',  # 未定义 - 灰色
    13: '#BDC3C7',  # 未知 - 浅灰色
    14: '#F1948A',  # 等待 - 粉红色
    15: '#82E0AA',  # 起下钻 - 浅绿色
    16: '#AED6F1',  # 起钻 - 浅蓝色
    17: '#F9E79F',  # 钻进 - 浅黄色
}

def convert_rigsta_to_numeric(rigsta_series):
    """
    将工况文本转换为数值

    :param rigsta_series: pandas Series，包含工况文本数据
    :return: pandas Series，包含对应的数值
    """
    return rigsta_series.map(RIGSTA_MAPPING).fillna(0)  # 未知工况映射为0

def plot_rigsta_timeline(ax, df, title_suffix=""):
    """
    绘制工况时间轴图

    :param ax: matplotlib轴对象
    :param df: 包含date和RIGSTA列的DataFrame
    :param title_suffix: 标题后缀
    """
    # 转换工况为数值
    df_plot = df.copy()
    df_plot['RIGSTA_NUM'] = convert_rigsta_to_numeric(df_plot['RIGSTA'])

    # 获取唯一的工况类型
    unique_rigsta = df_plot['RIGSTA'].dropna().unique()
    unique_nums = [RIGSTA_MAPPING.get(r, 0) for r in unique_rigsta]

    # 优化的工况可视化：使用更大的点和连线
    # 首先绘制连线显示工况变化趋势
    valid_data = df_plot[df_plot['RIGSTA_NUM'] > 0].copy()
    if len(valid_data) > 1:
        ax.plot(valid_data['date'], valid_data['RIGSTA_NUM'],
                color='gray', alpha=0.3, linewidth=0.5, zorder=1)

    # 绘制散点图，不同工况用不同颜色，增大点的大小
    for rigsta_text in unique_rigsta:
        rigsta_num = RIGSTA_MAPPING.get(rigsta_text, 0)
        mask = df_plot['RIGSTA'] == rigsta_text

        if rigsta_num > 0:  # 只绘制已知工况
            color = RIGSTA_COLORS.get(rigsta_num, '#BDC3C7')
            ax.scatter(df_plot.loc[mask, 'date'],
                      df_plot.loc[mask, 'RIGSTA_NUM'],
                      c=color, label=rigsta_text, alpha=0.8, s=8,
                      edgecolors='white', linewidth=0.3, zorder=2)

    # 设置Y轴标签，增大字体和间距
    y_ticks = sorted(unique_nums)
    y_labels = [list(RIGSTA_MAPPING.keys())[list(RIGSTA_MAPPING.values()).index(num)]
                for num in y_ticks if num > 0]

    ax.set_yticks(y_ticks)
    ax.set_yticklabels(y_labels, fontsize=10)
    ax.set_ylabel('钻井工况', rotation=0, labelpad=50, ha='right', fontsize=12)

    # 只在有标题后缀时才设置子图标题
    if title_suffix:
        ax.set_title(f'钻井工况变化{title_suffix}', fontsize=12)

    # 设置图例，优化显示
    if len(unique_rigsta) <= 10:  # 工况类型不太多时显示图例
        ax.legend(bbox_to_anchor=(1.02, 1), loc='upper left', fontsize=9,
                 frameon=True, fancybox=True, shadow=True)

    # 设置网格和样式
    ax.grid(True, alpha=0.3, linestyle='--')
    ax.set_ylim(0, max(RIGSTA_MAPPING.values()) + 1)

    # 优化背景色，使工况变化更突出
    ax.set_facecolor('#fafafa')

def generate_trend_plot(
    csv_path: str,
    output_folder: str,
    time_range: str = None,
    figsize: tuple = (22, 24),  # 减少高度，因为不包含工况子图
    dpi: int = 300
):
    """
    :param csv_path: CSV文件路径
    :param output_folder: 图片输出目录
    :param time_range: 时间范围字符串（用于标题）
    :param figsize: 图表尺寸
    :param dpi: 图片分辨率
    """
    # ========== 参数校验 ==========
    csv_file = Path(csv_path)
    if not csv_file.exists():
        raise FileNotFoundError(f"CSV文件不存在: {csv_path}")
    
    name_map = {
    # 原有特征
    'DEP': '井深',
    'BITDEP': '钻头位置',
    'HOKHEI': '大钩负荷',
    'DRITIME': '钻时',
    'WOB': '钻压',
    'HKLD': '大钩高度',
    'RPM': '转速',
    'TOR': '扭矩',

    # 新增特征
    'FLOWIN': '入口流量',
    'FLOWOUT': '出口流量',
    'SPP': '泵压',
    'CSIP': '套压',
    'RIGSTA': '钻井工况'
}

    # ========== 配置输出路径 ==========
    output_dir = Path(output_folder)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # 生成图片文件名（与CSV文件名保持一致）
    img_name = f"{csv_file.stem}.png"
    img_path = output_dir / img_name

    # ========== 数据准备 ==========
    try:
        df = pd.read_csv(csv_file)
        df['date'] = pd.to_datetime(df['date'])
    except Exception as e:
        raise ValueError(f"CSV文件读取失败: {str(e)}")

    # ========== 图表配置 ==========
    plt.style.use('seaborn-v0_8-colorblind')
    numeric_features = [
        'DEP', 'BITDEP', 'HOKHEI', 'DRITIME',
        'WOB', 'HKLD', 'RPM', 'TOR',
        'FLOWIN', 'FLOWOUT', 'SPP', 'CSIP'
    ]

    # 完整趋势图中不包含工况子图，只显示数值参数
    total_plots = len(numeric_features)

    # ========== 创建图表 ==========
    fig, axes = plt.subplots(total_plots, 1, figsize=(22, 1.2*total_plots), sharex=True)

    # 确保axes是数组
    if total_plots == 1:
        axes = [axes]

    # 绘制数值特征
    colors = plt.get_cmap('tab10')
    for i, col in enumerate(numeric_features):
        if col in df.columns:
            axes[i].plot(df['date'], df[col], color=colors(i%10), label=name_map[col])
            axes[i].set_ylabel(name_map[col], rotation=0, labelpad=20, ha='right')
            axes[i].legend(loc='best', frameon=True)
            axes[i].yaxis.set_major_formatter(ScalarFormatter(useOffset=False))
            axes[i].ticklabel_format(style='plain', axis='y')
        else:
            # 如果列不存在，显示空图
            axes[i].text(0.5, 0.5, f'{name_map[col]} - 数据缺失',
                        transform=axes[i].transAxes, ha='center', va='center')
            axes[i].set_ylabel(name_map[col], rotation=0, labelpad=20, ha='right')

    # ========== 时间轴格式设置 ==========
    for ax in axes:
        ax.xaxis.set_major_locator(mdates.HourLocator(interval=2))
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%m-%d %H:%M'))
        ax.xaxis.set_minor_locator(mdates.HourLocator(interval=1))

    # 调整底部标签角度
    axes[-1].tick_params(axis='x', rotation=0)

    # ========== 标题和布局 ==========
    title = f'{csv_file.stem} 参数趋势'
    if time_range:
        title += f' ({time_range})'
    fig.suptitle(title, y=0.98)
    plt.tight_layout(h_pad=1.5)

    # ========== 保存输出 ==========
    plt.savefig(img_path, bbox_inches='tight', dpi=dpi)
    plt.close()
    print(f"✅ 趋势图生成成功: {img_path}")
    return str(img_path)


def generate_rigsta_only_plot(
    csv_path: str,
    output_folder: str,
    time_range: str = None,
    figsize: tuple = (22, 8),
    dpi: int = 300
):
    """
    专门绘制工况变化图表

    :param csv_path: CSV文件路径
    :param output_folder: 图片输出目录
    :param time_range: 时间范围字符串（用于标题）
    :param figsize: 图表尺寸
    :param dpi: 图片分辨率
    """
    # ========== 参数校验 ==========
    csv_file = Path(csv_path)
    if not csv_file.exists():
        raise FileNotFoundError(f"CSV文件不存在: {csv_path}")

    # ========== 配置输出路径 ==========
    output_dir = Path(output_folder)
    output_dir.mkdir(parents=True, exist_ok=True)

    # 生成图片文件名
    img_name = f"{csv_file.stem}_工况变化.png"
    img_path = output_dir / img_name

    # ========== 数据准备 ==========
    try:
        df = pd.read_csv(csv_file, encoding='utf-8-sig')
        df['date'] = pd.to_datetime(df['date'])
    except Exception as e:
        raise ValueError(f"CSV文件读取失败: {str(e)}")

    # 检查是否有RIGSTA列
    if 'RIGSTA' not in df.columns or df['RIGSTA'].isna().all():
        print(f"警告：文件 {csv_file.name} 中没有有效的RIGSTA数据")
        return None

    # ========== 创建工况图表 ==========
    plt.style.use('seaborn-v0_8-colorblind')
    fig, ax = plt.subplots(1, 1, figsize=figsize)

    # 绘制工况时间轴（不传递标题后缀，避免重复）
    plot_rigsta_timeline(ax, df)

    # ========== 时间轴格式设置 ==========
    ax.xaxis.set_major_locator(mdates.HourLocator(interval=2))
    ax.xaxis.set_major_formatter(mdates.DateFormatter('%m-%d %H:%M'))
    ax.xaxis.set_minor_locator(mdates.HourLocator(interval=1))
    ax.tick_params(axis='x', rotation=45)

    # ========== 标题和布局 ==========
    title = f'{csv_file.stem} 钻井工况变化'
    if time_range:
        title += f' ({time_range})'
    fig.suptitle(title, y=0.95, fontsize=14)
    plt.tight_layout()

    # ========== 保存输出 ==========
    plt.savefig(img_path, bbox_inches='tight', dpi=dpi)
    plt.close()
    print(f"✅ 工况图生成成功: {img_path}")
    return str(img_path)


def batch_process_csv_files(
    input_folder: str = ".",
    output_folder: str = "charts",
    figsize: tuple = (22, 25),
    dpi: int = 200,
    include_rigsta: bool = True,
    rigsta_only: bool = False
):
    """
    批量处理目录中的所有CSV文件，生成趋势图

    :param input_folder: CSV文件所在目录，默认为当前目录
    :param output_folder: 图片输出目录，默认为 "charts"
    :param figsize: 图表尺寸
    :param dpi: 图片分辨率
    :param include_rigsta: 是否在综合图表中包含工况，默认True
    :param rigsta_only: 是否只生成工况图表，默认False
    """
    # ========== 扫描CSV文件 ==========
    input_path = Path(input_folder)
    csv_files = list(input_path.glob("*.csv"))

    if not csv_files:
        print("❌ 未找到任何CSV文件")
        return

    print(f"📁 发现 {len(csv_files)} 个CSV文件")
    print(f"📊 输出目录: {output_folder}")
    print("-" * 50)

    # ========== 创建输出目录 ==========
    output_path = Path(output_folder)
    output_path.mkdir(parents=True, exist_ok=True)

    # ========== 批量处理 ==========
    success_count = 0
    failed_files = []

    for i, csv_file in enumerate(csv_files, 1):
        try:
            print(f"🔄 正在处理 {i}/{len(csv_files)}: {csv_file.name}")

            if rigsta_only:
                # 只生成工况图表
                generate_rigsta_only_plot(
                    csv_path=str(csv_file),
                    output_folder=output_folder,
                    figsize=(22, 8),
                    dpi=dpi
                )
            else:
                # 生成综合趋势图（包含工况）
                generate_trend_plot(
                    csv_path=str(csv_file),
                    output_folder=output_folder,
                    figsize=figsize,
                    dpi=dpi
                )

                # 如果需要，额外生成单独的工况图
                if include_rigsta:
                    try:
                        generate_rigsta_only_plot(
                            csv_path=str(csv_file),
                            output_folder=output_folder,
                            figsize=(22, 8),
                            dpi=dpi
                        )
                    except Exception as e:
                        print(f"⚠️  工况图生成失败 {csv_file.name}: {str(e)}")

            success_count += 1

        except Exception as e:
            error_msg = f"❌ 处理失败 {csv_file.name}: {str(e)}"
            print(error_msg)
            failed_files.append((csv_file.name, str(e)))
            continue

    # ========== 处理结果总结 ==========
    print("-" * 50)
    print(f"📈 处理完成！")
    print(f"✅ 成功处理: {success_count} 个文件")
    print(f"❌ 处理失败: {len(failed_files)} 个文件")

    if failed_files:
        print("\n失败文件详情:")
        for filename, error in failed_files:
            print(f"  • {filename}: {error}")

    print(f"\n📂 所有图表已保存到: {output_path.absolute()}")
    return success_count, failed_files

# ===== 使用示例 =====
if __name__ == "__main__":
    print("选择处理模式:")
    print("1. 生成完整趋势图（仅数值参数，不含工况）")
    print("2. 只生成工况变化图")
    print("3. 生成完整趋势图 + 单独工况图（推荐）")

    choice = input("请输入选择 (1/2/3，默认为3): ").strip() or "3"

    if choice == "1":
        # 生成完整趋势图（不包含工况）
        batch_process_csv_files(
            input_folder=".",
            output_folder="charts",
            figsize=(22, 24),  # 调整高度，因为不包含工况图
            dpi=200,
            include_rigsta=False,  # 不额外生成单独工况图
            rigsta_only=False
        )
    elif choice == "2":
        # 只生成工况变化图
        batch_process_csv_files(
            input_folder=".",
            output_folder="charts",
            figsize=(22, 8),
            dpi=200,
            include_rigsta=False,
            rigsta_only=True
        )
    else:  # choice == "3" 或其他
        # 生成完整趋势图 + 单独工况图
        batch_process_csv_files(
            input_folder=".",
            output_folder="charts",
            figsize=(22, 24),  # 调整高度，因为完整图不包含工况
            dpi=200,
            include_rigsta=True,  # 额外生成单独工况图
            rigsta_only=False
        )

    # 单个文件处理示例：
    # generate_trend_plot(
    #     csv_path="your_file.csv",
    #     output_folder="charts",
    #     figsize=(22, 24),  # 调整高度，因为不包含工况图
    #     dpi=200
    # )

    # 单独生成工况图示例：
    # generate_rigsta_only_plot(
    #     csv_path="your_file.csv",
    #     output_folder="charts",
    #     figsize=(22, 8),
    #     dpi=200
    # )